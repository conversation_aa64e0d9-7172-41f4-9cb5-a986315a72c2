'use client'

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useTranslations } from "@/hooks/useTranslations"
import {
  ArrowRight,
  CheckCircle,
  Star,
  Shield,
  Zap,
  Globe,
  Users,
  GraduationCap,
  BookOpen,
  Target,
  Award,
  MessageCircle,
  Calendar,
  Download,
  TrendingUp,
  BarChart
} from "lucide-react"
import Link from "next/link"

interface ProductDetail {
  name: string
  description: string
  features: string[]
  techSpecs: {
    deployment: string
    security: string
    availability: string
    support: string
  }
  featureList?: Array<{
    title: string
    description: string
    features: Array<{
      name: string
      description: string
      icon: string
    }>
  }>
  demoVideo?: {
    url: string
    thumbnail: string
  }
  benefits?: Array<{
    title: string
    description: string
  }>
}

interface Props {
  product: ProductDetail
  productSlug: string
}

const iconMap = {
  GraduationCap,
  BookOpen,
  Target,
  Award,
  Shield,
  Zap,
  Globe,
  Users,
  CheckCircle,
  Star,
  TrendingUp,
  Bar<PERSON>hart
}

export function ProductDetailClient({ product, productSlug }: Props) {
  const t = useTranslations('educationManagement')
  const tCommon = useTranslations('common')

  const renderIcon = (iconName: string, className?: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap]
    return IconComponent ? <IconComponent className={className} /> : <GraduationCap className={className} />
  }

  return (
    <div className="relative isolate min-h-screen">
      {/* Background Effects */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/3 to-transparent dark:from-primary/10 dark:via-primary/5" />
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary/10 dark:bg-primary/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-primary/8 dark:bg-primary/15 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      {/* Hero Section */}
      <section className="relative py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-4xl text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mb-6"
            >
              <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium border-primary/20 bg-primary/5 dark:bg-primary/10">
                <GraduationCap className="w-4 h-4 mr-2" />
                {t('badge')}
              </Badge>
            </motion.div>

            <motion.h1
              className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                {product.name}
              </span>
            </motion.h1>

            <motion.p
              className="text-xl leading-8 text-muted-foreground mb-8 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              {product.description}
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              <Button asChild size="lg" className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80">
                <Link href="/contact-us">
                  {tCommon('contactUs')}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              
              <Button variant="outline" size="lg">
                <Calendar className="mr-2 h-5 w-5" />
                {tCommon('learnMore')}
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">核心功能</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              一站式教育培训管理平台
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {product.features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="relative overflow-hidden border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <CheckCircle className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-lg font-semibold group-hover:text-primary transition-colors">
                        {feature}
                      </h3>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Detailed Features Section */}
      {product.featureList && (
        <section className="py-16">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <motion.div
              className="text-center mb-12"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold mb-4">功能详情</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                深入了解我们教育管理系统的强大功能
              </p>
            </motion.div>

            {product.featureList.map((section, sectionIndex) => (
              <div key={sectionIndex} className="mb-16">
                <motion.div
                  className="text-center mb-8"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <h3 className="text-2xl font-bold mb-2">{section.title}</h3>
                  <p className="text-muted-foreground">{section.description}</p>
                </motion.div>

                <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                  {section.features.map((feature, featureIndex) => (
                    <motion.div
                      key={featureIndex}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: featureIndex * 0.1 }}
                      viewport={{ once: true }}
                      whileHover={{ y: -5 }}
                      className="group"
                    >
                      <Card className="relative overflow-hidden border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                        <CardContent className="p-6">
                          <div className="flex items-start gap-4 mb-4">
                            <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                              {renderIcon(feature.icon, "h-6 w-6 text-white")}
                            </div>
                            <div>
                              <h4 className="text-lg font-semibold group-hover:text-primary transition-colors mb-2">
                                {feature.name}
                              </h4>
                              <p className="text-sm text-muted-foreground">
                                {feature.description}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Technical Specifications */}
      <section className="py-16 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('techSpecsTitle')}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              企业级技术规格，确保系统稳定可靠
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            {Object.entries(product.techSpecs).map(([key, value], index) => (
              <motion.div
                key={key}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="relative overflow-hidden border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <CardContent className="p-6 text-center">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg mb-4 inline-block group-hover:scale-110 transition-transform duration-300">
                      <Shield className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                      {t(`techSpecs.${key}`)}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {value}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      {product.benefits && (
        <section className="py-16">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <motion.div
              className="text-center mb-12"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold mb-4">产品优势</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                为什么选择我们的教育培训管理系统
              </p>
            </motion.div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {product.benefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -5 }}
                  className="group"
                >
                  <Card className="relative overflow-hidden border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                          <Star className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold group-hover:text-primary transition-colors mb-2">
                            {benefit.title}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            {benefit.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Education Stats */}
      <section className="py-16 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('educationResults')}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              数据说话，见证我们的教育成果
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg mb-4 inline-block">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-primary mb-2">100K+</h3>
                  <p className="text-sm text-muted-foreground">{t('students')}</p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg mb-4 inline-block">
                    <BookOpen className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-primary mb-2">1000+</h3>
                  <p className="text-sm text-muted-foreground">{t('onlineCourses')}</p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg mb-4 inline-block">
                    <TrendingUp className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-primary mb-2">98%</h3>
                  <p className="text-sm text-muted-foreground">{t('satisfaction')}</p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg mb-4 inline-block">
                    <Award className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-primary mb-2">80%</h3>
                  <p className="text-sm text-muted-foreground">{t('efficiencyImprovement')}</p>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-br from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/20">
        <div className="mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('readyToStart')}</h2>
            <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
              {t('ctaDescription')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button asChild size="lg" className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80">
                <Link href="/contact-us">
                  <MessageCircle className="w-5 h-5 mr-2" />
                  {tCommon('contactUs')}
                </Link>
              </Button>
              <Button variant="outline" size="lg">
                <Download className="w-5 h-5 mr-2" />
                下载产品手册
              </Button>
            </div>

            {/* Additional CTA Info */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg mb-4 inline-block">
                  <Calendar className="h-6 w-6 text-white" />
                </div>
                <h3 className="font-semibold mb-2">免费试用</h3>
                <p className="text-sm text-muted-foreground">30天免费试用期，体验完整功能</p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg mb-4 inline-block">
                  <MessageCircle className="h-6 w-6 text-white" />
                </div>
                <h3 className="font-semibold mb-2">专业支持</h3>
                <p className="text-sm text-muted-foreground">7x24小时技术支持，快速响应</p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg mb-4 inline-block">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <h3 className="font-semibold mb-2">安全保障</h3>
                <p className="text-sm text-muted-foreground">企业级安全防护，数据安全可靠</p>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
